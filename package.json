{"name": "marsmart-bi", "version": "0.0.20", "scripts": {"dev": "vite", "build": "node version-update.js && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "0.2.7", "@tinymce/tinymce-vue": "4.0.7", "axios": "0.24.0", "countup.js": "2.8.0", "datajs": "1.0.3", "dayjs": "1.11.7", "echarts": "5.4.1", "element-plus": "^2.9.3", "elui-china-area-dht": "2.0.0", "eslint": "8.35.0", "html2canvas": "^1.4.1", "image-conversion": "2.1.1", "js-cookie": "3.0.1", "js-md5": "0.7.3", "node-printer": "1.0.4", "npm": "10.5.0", "nprogress": "0.2.0", "print-js": "^1.6.0", "qs": "6.11.0", "sprintf-js": "1.1.3", "tinymce": "5.10.3", "util": "0.12.5", "utils": "0.3.1", "vue": "3.2.47", "vue-cropper": "1.0.5", "vue-draggable-plus": "^0.4.1", "vue-router": "4.1.6", "vuex": "4.1.0", "xlsx": "0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "2.3.4", "unplugin-auto-import": "0.5.11", "unplugin-vue-components": "0.17.21", "vite": "2.9.15"}}