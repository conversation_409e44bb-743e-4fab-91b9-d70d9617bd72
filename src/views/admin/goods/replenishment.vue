<template>
    <div class="main_box">
        <!-- 补货建议信息 -->
        <el-card class="section-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="header-icon">
                        <ShoppingCartFull/>
                    </el-icon>
                    <span class="header-title">补货建议信息</span>
                </div>
            </template>

            <div class="search-form">
                <!-- 第一行：基础搜索条件 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">选择月份</label>
                            <DateTimePicker v-model:com_value="goodsMonth" com_type="month" com_placeholder="选择月份"></DateTimePicker>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">商品编码</label>
                            <el-input v-model="skuid" placeholder="请输入商品编码" @keyup.enter.native="searchPurchase" clearable></el-input>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">商品类型</label>
                            <el-select v-model="goodsProperty" popper-class="select_box_style" clearable placeholder="食品/用品">
                                <el-option label="食品" value="1"/>
                                <el-option label="用品" value="2"/>
                            </el-select>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">商品状态</label>
                            <el-select v-model="goodsEnabled" popper-class="select_box_style" clearable placeholder="商品状态">
                                <el-option label="未淘汰" value="1"/>
                                <el-option label="淘汰" value="-1"/>
                            </el-select>
                        </div>
                    </el-col>
                </el-row>

                <!-- 第二行：品牌和供应商 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">品牌</label>
                            <brandSelect v-model:brand_name="brand"></brandSelect>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">供应商</label>
                            <SupplierSelect v-model:supplier_id="seller"></SupplierSelect>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">计算逻辑</label>
                            <el-select v-model="algorithm" popper-class="select_box_style" placeholder="请选择计算逻辑">
                                <el-option label="1.0计算逻辑" value="1"/>
                                <el-option label="2.0计算逻辑" value="2"/>
                                <el-option label="3.0计算逻辑" value="3"/>
                                <el-option label="4.0计算逻辑" value="4"/>
                            </el-select>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3" v-if="algorithm == 4">
                        <div class="form-item">
                            <label class="form-label">补货天数</label>
                            <el-select v-model="replenishmentDay" popper-class="select_box_style" placeholder="请选择补货天数">
                                <el-option label="30天" value="30"/>
                                <el-option label="45天" value="45"/>
                                <el-option label="60天" value="60"/>
                            </el-select>
                        </div>
                    </el-col>
                </el-row>

                <!-- 第三行：补货系数 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">补货系数</label>
                            <el-input-number
                                v-model="coefficient"
                                placeholder="补货系数"
                                :min="1"
                                :max="10"
                                :step="0.1"
                                controls-position="right"
                                @keyup.enter.native="searchPurchase"
                                style="width: 100%"
                            />
                        </div>
                    </el-col>
                </el-row>

                <!-- 第四行：操作按钮 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="6">
                        <div class="form-item button-item-left">
                            <el-button type="warning" @click="searchPurchase" :loading="explodeLock">
                                <el-icon><Download /></el-icon>
                                导出补货建议
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </el-card>

        <!-- 调拨计划查询 -->
        <el-card class="section-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="header-icon">
                        <Files/>
                    </el-icon>
                    <span class="header-title">调拨计划查询</span>
                </div>
            </template>

            <div class="search-form">
                <!-- 第一行：基础搜索条件 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">商品编码</label>
                            <el-input v-model="transferSkuid" placeholder="请输入商品编码" clearable></el-input>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">商品状态</label>
                            <el-select v-model="transferGoodsEnabled" popper-class="select_box_style" clearable placeholder="商品状态">
                                <el-option label="启用" value="2"/>
                                <el-option label="禁用" value="1"/>
                            </el-select>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">是否淘汰</label>
                            <el-select v-model="transferGoodsEliminate" popper-class="select_box_style" clearable placeholder="是否淘汰">
                                <el-option label="是" value="1"/>
                                <el-option label="否" value="2"/>
                            </el-select>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
                        <div class="form-item">
                            <label class="form-label">商品分类</label>
                            <el-select v-model="transferGoodsCategory" popper-class="select_box_style" multiple clearable placeholder="选择表">
                                <el-option label="食品主粮" value="1"/>
                                <el-option label="罐头、零食" value="2"/>
                                <el-option label="衣服、胸背、牵引" value="3"/>
                                <el-option label="其他用品" value="4"/>
                                <el-option label="保健品" value="5"/>
                                <el-option label="玩具" value="6"/>
                            </el-select>
                        </div>
                    </el-col>
                </el-row>

                <!-- 第二行：品牌、店铺和算法 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">品牌</label>
                            <brandSelect v-model:brand_name="transferBrand"></brandSelect>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
                        <div class="form-item">
                            <label class="form-label">选择店铺</label>
                            <ShopSelect v-model:shop_key="transferShopId" :is_multiple="true"></ShopSelect>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">调拨逻辑</label>
                            <el-select v-model="transferAlgorithm" @change="handleCityChange" popper-class="select_box_style" placeholder="调拨逻辑">
                                <el-option label="1.0调拨逻辑" value="1"/>
                                <el-option label="2.0调拨逻辑" value="2"/>
                            </el-select>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
                        <div class="form-item">
                            <label class="form-label">展示字段</label>
                            <el-select v-model="transferShowField" popper-class="select_box_style" placeholder="展示字段">
                                <el-option label="精简字段" value="1"/>
                                <el-option label="完整字段" value="2"/>
                                <el-option label="导入模板字段" value="3"/>
                            </el-select>
                        </div>
                    </el-col>
                </el-row>

                <!-- 第三行：操作按钮 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="6">
                        <div class="form-item button-item-left">
                            <el-button type="warning" @click="searchTransfer" :loading="explodeLock">
                                <el-icon><Download /></el-icon>
                                导出调拨计划
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </el-card>

        <!-- 库存量统计 -->
        <el-card class="section-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="header-icon">
                        <Box/>
                    </el-icon>
                    <span class="header-title">库存量统计</span>
                </div>
            </template>

            <div class="search-form">
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                        <div class="form-item">
                            <label class="form-label">查询截止日期</label>
                            <DateTimePicker v-model:com_value="inventoryBeginTime" com_type="date" com_placeholder="选择查询截止日期"></DateTimePicker>
                        </div>
                    </el-col>
                </el-row>

                <!-- 操作按钮 -->
                <el-row :gutter="16" class="search-row">
                    <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="6">
                        <div class="form-item button-item-left">
                            <el-button type="warning" @click="searchInventory" :loading="explodeLock">
                                <el-icon><Download /></el-icon>
                                导出库存统计
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </el-card>

    </div>
</template>

<script setup>
    import {onMounted} from '@vue/runtime-core';
    import {ref,inject} from 'vue'
    import {ShoppingCartFull,Files,Box,Download} from "@element-plus/icons-vue";

    import {
        getSupplierPersonList
    } from '@/api/goods';
    import {
        getReplenishment,
        getPriceContrast,
        getInventoryByCategorize,
    } from '@/api/reportforms';
    import {
        getTransferPlan
    } from '@/api/transfer';
    import DateTimePicker from "@/components/search/date-time-picker.vue";
    import BrandSelect from "@/components/search/brand-select.vue";
    import SupplierSelect from "@/components/search/supplier-select.vue";
    import ShopSelect from "@/components/search/shop-select.vue";
    import {getLoading} from "@/utils/common";

    const downloadAnimateElement = inject('downloadAnimateElement');

    // 导出按钮锁
    const explodeLock = ref(false)

    // 调拨计划
    const transferBeginTime = ref("")
    const transferEndTime = ref("")
    const transferSkuid = ref('')
    const transferGoodsEnabled = ref('2')
    const transferGoodsEliminate = ref('')
    const transferGoodsCategory = ref(['1'])
    const transferSeller = ref('')
    const transferBrand = ref('')
    const transferSupplierPerson = ref('')
    const transferShopId = ref([])
    const transferShowField = ref('1')
    const transferAlgorithm = ref('2')
    // const transferCityCode = ref('110000')

    // 补货建议搜索
    const beginTime = ref("")
    const endTime = ref("")
    const goodsMonth = ref("")
    const skuid = ref('')
    const goodsEnabled = ref('1')
    const goodsProperty = ref('')
    const seller = ref('')
    const brand = ref('')
    const supplierPerson = ref('')
    const replenishmentDay = ref('30')
    const algorithm = ref('4')
    const coefficient = ref(1.5)

    // 库存余量
    const inventoryDate = ref('')

    // 库存余量查询
    const inventoryBeginTime = ref('')

    // 供应商负责人列表
    const supplierPersonList = ref([])


    // 导出补货建议信息
    const searchPurchase = async (e) => {

        if (!goodsMonth.value) {
            ElMessage.error('请选择补货月份');
            return;
        }

        if (explodeLock.value == true) {
            return false;
        }

        explodeLock.value = true

        let loading = getLoading()
        let res = await getReplenishment({
            begintime: beginTime.value,
            endtime: endTime.value,
            month: goodsMonth.value,
            sku:skuid.value,
            enabled: goodsEnabled.value,
            property: goodsProperty.value,
            brand:brand.value,
            supplier:seller.value,
            replenishment_day: replenishmentDay.value,
            algorithm: algorithm.value,
            coefficient: coefficient.value,
            supplier_person: supplierPerson.value
        })
        loading.close()

        explodeLock.value = false

        if (res.status != 200) {
            ElMessage.error(res.msg);
            return;
        }

        downloadAnimateElement(e.x,e.y)

        // alert('/replenishment/'+res.data.file_name+'.xlsx')
        // console.log('/replenishment/'+res.data.file_name+'.xlsx')

        // window.location.href = '/reportforms/replenishment/'+res.data.file_name+'.xlsx';
    }

    // 调拨计划
    const searchTransfer = async (e) => {

        // 添加判断逻辑，如果选择导入模板字段那么食品主粮只能单独导出不能和其他报表一起导出
        if (transferShowField.value === '3') {
            if (transferGoodsCategory.value.length > 1 && transferGoodsCategory.value.indexOf('1') >= 0) {
                ElMessage.error('食品主粮不能与其他报表一起导出');
                return;
            }

            if (transferGoodsEnabled.value !== '' || transferGoodsEliminate.value !== '') {
                ElMessage.warning('请注意您导出的模板表包含商品状态或是否淘汰筛选字段，这样会导致导出的数据不全。');
            }
        }

        if (transferShopId.value.length < 1) {
            ElMessage.error('请选择要导出的店铺');
            return;
        }

        if (transferGoodsCategory.value.length < 1) {
            ElMessage.error('请选择要导出的表');
            return;
        }

        if (explodeLock.value == true) {
            return false;
        }

        explodeLock.value = true

        let loading = getLoading()
        let res = await getTransferPlan({
            begintime: transferBeginTime.value,
            endtime: transferEndTime.value,
            sku:transferSkuid.value,
            enabled: transferGoodsEnabled.value,
            eliminate: transferGoodsEliminate.value,
            custom_category: transferGoodsCategory.value.join(','),
            brand:transferBrand.value,
            supplier:transferSeller.value,
            supplier_person: transferSupplierPerson.value,
            shop_id: transferShopId.value.join(','),
            show_field: transferShowField.value,
            algorithm:transferAlgorithm.value,
            // city_code: transferCityCode.value,
        })
        loading.close()

        explodeLock.value = false

        if (res.status != 200) {
            ElMessage.error(res.msg);
            return;
        }

        downloadAnimateElement(e.x,e.y)
        // window.location.href = res.data.file_path+res.data.file_name+'.xlsx';
    }

    // 库存余量查询
    const searchInventory = async (e) => {

        if (!inventoryBeginTime.value) {
            ElMessage.error('请选择查询截止日期');
            return;
        }

        if (explodeLock.value === true) {
            return false;
        }

        explodeLock.value = true

        let loading = getLoading()
        let res = await getInventoryByCategorize({
            endtime: inventoryBeginTime.value,
        })

        loading.close()
        explodeLock.value = false
        if (res.status != 200) {
            ElMessage.error(res.msg);
            return;
        }
        downloadAnimateElement(e.x, e.y)
    }

    // 获取供应商负责人
    const getAllSupplierPersonList = async () => {

        let res = await getSupplierPersonList();

        if (res.data.length < 1) {
            ElMessage.error("获取分类信息失败");
            return;
        }

        supplierPersonList.value = res.data;
    }

    // 初始化加载
    onMounted(() => {

        getAllSupplierPersonList();

        // 获取当前月
        // let now = dayjs()
        // monthOrderTime.value = now.format('YYYY-MM-DD');
    })

    const openNewWindow = (row) => {
        window.open(row.url, '_blank');
    }

    const getWebTypeDesc = (typeid) => {
        return typeDescList[typeid]
    }

    // 处理城市变化，重置门店选择
    const handleCityChange = () => {
        // 当销售单元变化时，重置门店选择
        transferShopId.value = []
    }

</script>

<style scoped>
    .main_box {
        height: 100%;
        overflow-y: auto;
        padding: 20px;
        background-color: #f5f7fa;
    }

    /* 卡片样式 */
    .section-card {
        margin-bottom: 24px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;
    }

    .section-card:last-child {
        margin-bottom: 0;
    }

    /* 卡片头部样式 */
    .card-header {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
    }

    .header-icon {
        margin-right: 8px;
        font-size: 18px;
        color: #409eff;
    }

    .header-title {
        font-size: 16px;
    }

    /* 搜索表单样式 */
    .search-form {
        padding: 16px 0;
    }

    .search-row {
        margin-bottom: 16px;
    }

    .search-row:last-child {
        margin-bottom: 0;
    }

    /* 表单项样式 */
    .form-item {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .form-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 6px;
        font-weight: 500;
        line-height: 1.4;
    }

    /* 按钮项特殊样式 */
    .button-item {
        justify-content: flex-end;
        align-items: flex-end;
        padding-top: 22px;
    }

    /* 按钮项居左样式 */
    .button-item-left {
        justify-content: flex-start;
        align-items: flex-end;
        padding-top: 22px;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .main_box {
            padding: 12px;
        }

        .search-row {
            margin-bottom: 12px;
        }

        .form-label {
            font-size: 13px;
            margin-bottom: 4px;
        }

        .button-item {
            padding-top: 16px;
            justify-content: center;
        }

        .button-item-left {
            padding-top: 16px;
            justify-content: flex-start;
        }
    }

    /* Element Plus 组件样式覆盖 */
    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-input-number) {
        width: 100%;
    }

    :deep(.el-card__header) {
        padding: 16px 20px;
        background-color: #fafbfc;
        border-bottom: 1px solid #ebeef5;
    }

    :deep(.el-card__body) {
        padding: 20px;
    }

    /* 按钮样式 */
    :deep(.el-button) {
        height: 36px;
        padding: 8px 20px;
        font-weight: 500;
    }

    /* 选择器下拉样式 */
    :deep(.el-select__placeholder) {
        color: #c0c4cc;
    }

    /* 输入框样式 */
    :deep(.el-input__inner) {
        height: 36px;
    }

    /* 数字输入框样式 */
    :deep(.el-input-number .el-input__inner) {
        text-align: left;
    }
</style>