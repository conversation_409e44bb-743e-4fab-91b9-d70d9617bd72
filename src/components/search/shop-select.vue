<template>
    <div class="input_box">
        <el-select v-model="shop" :multiple="is_multiple" :collapse-tags="is_multiple" :collapse-tags-tooltip="is_multiple" filterable :size="com_size" @change="changeValue" popper-class="select_box_style" :placeholder="com_placeholder" clearable>
            <!--el-option-group v-for="(cityItem,city) in cityShopList" :label="city">
                <el-option v-for="item in cityItem" :label="item.name" :value="item.id" />
            </el-option-group-->
            <template #header v-if="is_multiple">
                <el-checkbox v-model="shopCheckAll" @change="handleCheckAll">全选</el-checkbox>
            </template>
            <el-option v-for="item in shopList" :label="item.name" :value="item.id" />
        </el-select>
    </div>
</template>

<script setup>
    import {onMounted} from '@vue/runtime-core';
    import {ref,watchEffect} from 'vue'
    import {shopName} from '@/api/sale';

    const shop = ref('')
    const shopList = ref([])
    const cityShopList = ref({})
    const shopCheckAll = ref(false)

    var shopKeyData = {}
    var shopIdData = {}
    var cityCode = ''


    const emit = defineEmits(["update:shop_id","update:shop_key","update:shop_name"])

    const props = defineProps({
        shop_id: {
            type: [String, Array],
            default: ''
        },
        shop_key: {
            type: [String, Array],
            default: ''
        },
        city_code: {
            type: String,
            default: ''
        },
        shop_name: {
            type: [String, Array],
            default: ''
        },
        sync_data: {
            type: Function,
            default: ()=>{}
        },
        com_size: {
            type: String,
            default: 'default'
        },
        com_placeholder: {
            type: String,
            default: '销售单元'
        },
        com_not_all: {
            type: Boolean,
            default: false
        },
        is_multiple: {
            type: Boolean,
            default: false
        }
    })

    // if (props.is_multiple) {
    //     const shop = ref([])
    // } else {
    //
    // }
    cityCode = props.city_code

    // 全选
    const handleCheckAll = () => {
        if (shopCheckAll.value) {
            shop.value = [];
            shopList.value.forEach(item => {
                if (!shop.value.includes(item.id)) {
                    shop.value.push(item.id)
                }
            })
            changeValue(shop.value)
        } else {
            shop.value = [];
        }
    };

    const changeValue = (e) => {

        if (!e) {
            if (props.is_multiple) {
                emit("update:shop_id", [])
                emit("update:shop_key", [])
                emit("update:shop_name", [])
            } else {
                emit("update:shop_id", '')
                emit("update:shop_key", '')
                emit("update:shop_name", '')
            }
            return;
        }

        if (props.is_multiple) {

            let shopId = []
            let shopKey = []
            let shopNameList = []

            e.forEach(item => {
                shopId.push(shopIdData[item].shop_id)
                shopKey.push(shopIdData[item].shop_key)
                shopNameList.push(shopIdData[item].name)
            })

            emit("update:shop_id", shopId)
            emit("update:shop_key", shopKey)
            emit("update:shop_name", shopNameList)
            return;
        }

        emit("update:shop_id", shopIdData[e].shop_id)
        emit("update:shop_key", shopIdData[e].shop_key)
        emit("update:shop_name", shopIdData[e].name)
    }

    // 获取门店
    const getAllshopList = async () => {

        let res = await shopName({});

        if (res.data.length < 1) {
            ElMessage.error("获取销售单元信息失败");
            return;
        }

        // 城市筛选,如果city_code不为空,则筛选出对应城市的门店
        if (props.city_code != '') {
            res.data = res.data.filter(item => item.city_code == props.city_code)
        }

        shopList.value = res.data;
        if (!props.com_not_all) {
            // shopList.value.unshift({
            //     id: "0",
            //     shop_id:"0",
            //     shop_key:"0",
            //     name: "全部"
            // })
        }

        shopList.value.forEach((item, index) => {
            shopKeyData[item.shop_key] = item;
            shopIdData[item.shop_id] = item;

            // 收集city_name到 cityList
            if (!cityShopList.value[item.city_name]) {
                cityShopList.value[item.city_name] = []
            }
            cityShopList.value[item.city_name].push(item)
        })

        console.log(cityShopList)

        setValue()

        props.sync_data(shopList.value)
    }

    // 初始化加载
    onMounted(() => {

        getAllshopList()

        // 监控值变化
        watchEffect(() => {

            if (props.city_code && cityCode != props.city_code) {
                // 当city_code变化时，重置shop值并重新获取门店列表
                shop.value = props.is_multiple ? [] : ''
                shopList.value = []
                // shopKeyData = {}
                // shopIdData = {}
                getAllshopList()
                cityCode = props.city_code
            }
            
            
            if (shopList.value.length > 0) {
                setValue()
            }
        })
    })

    // 赋值
    const setValue = () => {

        if (props.is_multiple){
            shop.value = []
        } else {
            shop.value = ''
        }

        if (props.shop_id != '') {
            if (props.is_multiple && Array.isArray(props.shop_id)) {
                props.shop_id.forEach(item => {
                    if (shopIdData[item]) {
                        shop.value.push(shopIdData[item].id)
                    }
                })
            } else {
                shop.value = shopIdData[props.shop_id].id
            }
        }

        if (props.shop_key != '') {

            // 如果  props.is_multiple 为true 则切割 props.shop_key 循环复制给shop
            if (props.is_multiple && Array.isArray(props.shop_key)) {
                props.shop_key.forEach(item => {
                    if (shopKeyData[item]) {
                        shop.value.push(shopKeyData[item].id)
                    }
                })
            } else {
                shop.value = shopKeyData[props.shop_key].id
            }
        }
    }

</script>

<style scoped>
.drag_background {
    display: none;
    width: 100%;
    height: 100%;
    z-index: 9999;
    position: absolute;
    top: 0;
    left: 0;
}
.drag_background_line {
    position: absolute;
    cursor: n-resize;
    width: 100%;
    background-color: rgb(204, 204, 204);
    height: 5px;
    z-index: 9999;
    top: 0;
}
</style>